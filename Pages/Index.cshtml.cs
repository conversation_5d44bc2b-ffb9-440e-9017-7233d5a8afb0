using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Threading.Tasks;

public class IndexModel : PageModel
{
    [BindProperty]
    public string City { get; set; }
    public WeatherResult WeatherResult { get; set; }
    public string ErrorMessage { get; set; }

    public void OnGet()
    {
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (string.IsNullOrWhiteSpace(City))
        {
            ErrorMessage = "Please enter a city name.";
            return Page();
        }
        try
        {
            var service = new WeatherService();
            WeatherResult = await service.GetWeatherAsync(City);
            if (WeatherResult == null)
            {
                ErrorMessage = "Could not find weather data for the specified city.";
            }
        }
        catch (System.Exception ex)
        {
            ErrorMessage = "Error fetching weather data: " + ex.Message;
        }
        return Page();
    }
}

public class WeatherResult
{
    public string CityName { get; set; }
    public double Temperature { get; set; }
    public string Description { get; set; }
    public int Humidity { get; set; }
    public double WindSpeed { get; set; }
} 