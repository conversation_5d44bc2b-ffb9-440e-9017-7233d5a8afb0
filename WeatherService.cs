using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

public class WeatherService
{
    private const string ApiKey = "15782ef7869f2a764d412d173acfcb80";
    private const string BaseUrl = "https://api.openweathermap.org/data/2.5/weather";

    public async Task<WeatherResult> GetWeatherAsync(string city)
    {
        using var client = new HttpClient();
        var url = $"{BaseUrl}?q={city}&appid={ApiKey}&units=metric";
        var response = await client.GetAsync(url);
        if (!response.IsSuccessStatusCode)
            return null;
        var json = await response.Content.ReadAsStringAsync();
        using var doc = JsonDocument.Parse(json);
        var root = doc.RootElement;
        return new WeatherResult
        {
            CityName = root.GetProperty("name").GetString(),
            Temperature = root.GetProperty("main").GetProperty("temp").GetDouble(),
            Description = root.GetProperty("weather")[0].GetProperty("description").GetString(),
            Humidity = root.GetProperty("main").GetProperty("humidity").GetInt32(),
            WindSpeed = root.GetProperty("wind").GetProperty("speed").GetDouble()
        };
    }
} 