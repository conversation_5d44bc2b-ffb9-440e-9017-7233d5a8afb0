is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = WeatherApp
build_property.RootNamespace = WeatherApp
build_property.ProjectDir = /Users/<USER>/Desktop/Dev/cSharp/WeatherApp/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = /Users/<USER>/Desktop/Dev/cSharp/WeatherApp
build_property._RazorSourceGeneratorDebug = 

[/Users/<USER>/Desktop/Dev/cSharp/WeatherApp/Pages/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXMvRXJyb3IuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Desktop/Dev/cSharp/WeatherApp/Pages/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXMvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Desktop/Dev/cSharp/WeatherApp/Pages/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXMvUHJpdmFjeS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Desktop/Dev/cSharp/WeatherApp/Pages/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXMvU2hhcmVkL19WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Desktop/Dev/cSharp/WeatherApp/Pages/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXMvX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Desktop/Dev/cSharp/WeatherApp/Pages/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXMvX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Desktop/Dev/cSharp/WeatherApp/Pages/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXMvU2hhcmVkL19MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-421rdq8cat
