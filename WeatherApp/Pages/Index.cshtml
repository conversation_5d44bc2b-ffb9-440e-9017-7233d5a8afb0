﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Home page";
}
<div class="alert alert-info text-center">This is a test update.</div>
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body">
                    <h2 class="card-title mb-4 text-center">Weather App</h2>
                    <form method="post">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="City" placeholder="Enter city name" value="@Model.City" required />
                            <button class="btn btn-primary" type="submit">Get Weather</button>
                        </div>
                    </form>
                    @if (Model.WeatherResult != null)
                    {
                        <div class="card mt-4">
                            <div class="card-body text-center">
                                <h4 class="card-title">@Model.WeatherResult.CityName</h4>
                                <p class="card-text display-6">@Model.WeatherResult.Temperature &deg;C</p>
                                <p class="card-text">@Model.WeatherResult.Description</p>
                                <p class="card-text"><small>Humidity: @Model.WeatherResult.Humidity% | Wind: @Model.WeatherResult.WindSpeed m/s</small></p>
                            </div>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger mt-4">@Model.ErrorMessage</div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
