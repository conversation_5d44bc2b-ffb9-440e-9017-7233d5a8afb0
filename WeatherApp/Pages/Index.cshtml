﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Weather App - Get Current Weather";
}

<style>
    .main-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: calc(100vh - 200px);
        font-family: 'Poppins', sans-serif;
        padding: 2rem 0;
    }

    .weather-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .weather-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 20px 20px 0 0;
        padding: 2rem;
        text-align: center;
    }

    .weather-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .search-section {
        padding: 2rem;
    }

    .search-input {
        border: 2px solid #e9ecef;
        border-radius: 50px;
        padding: 15px 25px;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        border-color: #4facfe;
        box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        outline: none;
    }

    .search-btn {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 50px;
        padding: 15px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
    }

    .search-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
    }

    .weather-result {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-top: 2rem;
        text-align: center;
    }

    .temperature {
        font-size: 4rem;
        font-weight: 300;
        margin: 1rem 0;
    }

    .weather-details {
        display: flex;
        justify-content: space-around;
        margin-top: 2rem;
        flex-wrap: wrap;
    }

    .weather-detail-item {
        text-align: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        margin: 0.5rem;
        flex: 1;
        min-width: 120px;
    }

    .weather-detail-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .alert-custom {
        border-radius: 15px;
        border: none;
        padding: 1rem 2rem;
        margin-bottom: 2rem;
    }

    .alert-info-custom {
        background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
        color: white;
    }

    .alert-danger-custom {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
    }

    @@media (max-width: 768px) {
        .weather-container {
            margin: 1rem;
        }

        .temperature {
            font-size: 3rem;
        }

        .weather-details {
            flex-direction: column;
        }
    }
</style>

<div class="main-content">
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-8 col-sm-10">
            <div class="alert alert-info-custom alert-custom text-center">
                <i class="fas fa-info-circle me-2"></i>
                Welcome to your personal weather station! 🌤️
            </div>

            <div class="weather-container">
                <div class="weather-header">
                    <div class="weather-icon">
                        <i class="fas fa-cloud-sun"></i>
                    </div>
                    <h1 class="mb-0">Weather Forecast</h1>
                    <p class="mb-0 opacity-75">Get real-time weather information</p>
                </div>

                <div class="search-section">
                    <form method="post">
                        <div class="input-group">
                            <input type="text" class="form-control search-input" name="City"
                                   placeholder="🔍 Enter city name (e.g., London, Tokyo, New York)"
                                   value="@Model.City" required />
                            <button class="btn btn-primary search-btn" type="submit">
                                <i class="fas fa-search me-2"></i>Get Weather
                            </button>
                        </div>
                    </form>

                    @if (Model.WeatherResult != null)
                    {
                        <div class="weather-result">
                            <h3 class="mb-3">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                @Model.WeatherResult.CityName
                            </h3>
                            <div class="temperature">
                                @Model.WeatherResult.Temperature°C
                            </div>
                            <p class="fs-5 text-capitalize mb-0">
                                @Model.WeatherResult.Description
                            </p>

                            <div class="weather-details">
                                <div class="weather-detail-item">
                                    <div class="weather-detail-icon">
                                        <i class="fas fa-tint"></i>
                                    </div>
                                    <div>
                                        <strong>@Model.WeatherResult.Humidity%</strong>
                                        <br><small>Humidity</small>
                                    </div>
                                </div>
                                <div class="weather-detail-item">
                                    <div class="weather-detail-icon">
                                        <i class="fas fa-wind"></i>
                                    </div>
                                    <div>
                                        <strong>@Model.WeatherResult.WindSpeed m/s</strong>
                                        <br><small>Wind Speed</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger-custom alert-custom mt-4">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @Model.ErrorMessage
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
</div>
