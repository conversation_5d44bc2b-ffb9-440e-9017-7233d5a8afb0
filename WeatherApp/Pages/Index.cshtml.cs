using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Threading.Tasks;

namespace WeatherApp.Pages;

public class IndexModel : PageModel
{
    private readonly ILogger<IndexModel> _logger;

    public IndexModel(ILogger<IndexModel> logger)
    {
        _logger = logger;
    }

    [BindProperty]
    public string City { get; set; }
    public WeatherResult WeatherResult { get; set; }
    public string ErrorMessage { get; set; }

    public void OnGet()
    {
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (string.IsNullOrWhiteSpace(City))
        {
            ErrorMessage = "Please enter a city name.";
            return Page();
        }
        try
        {
            var service = new WeatherService();
            WeatherResult = await service.GetWeatherAsync(City);
            if (WeatherResult == null)
            {
                ErrorMessage = "Could not find weather data for the specified city.";
            }
        }
        catch (System.Exception ex)
        {
            ErrorMessage = "Error fetching weather data: " + ex.Message;
        }
        return Page();
    }
}
