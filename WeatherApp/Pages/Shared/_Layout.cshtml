﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - WeatherApp</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/WeatherApp.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
        }

        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            padding: 1rem 0;
        }

        .navbar-brand-custom {
            color: white !important;
            font-weight: 600;
            font-size: 1.5rem;
        }

        .navbar-brand-custom:hover {
            color: #f8f9fa !important;
        }

        .nav-link-custom {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link-custom:hover {
            color: white !important;
            transform: translateY(-1px);
        }

        .footer-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-top: auto;
        }

        .footer-custom a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .footer-custom a:hover {
            color: white;
        }

        html, body {
            height: 100%;
        }

        #root {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        main {
            flex: 1;
        }
    </style>

    <div id="root">
        <header>
            <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-custom">
                <div class="container">
                    <a class="navbar-brand navbar-brand-custom" asp-area="" asp-page="/Index">
                        <i class="fas fa-cloud-sun me-2"></i>WeatherApp
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                            aria-expanded="false" aria-label="Toggle navigation" style="border-color: rgba(255,255,255,0.3);">
                        <span class="navbar-toggler-icon" style="background-image: url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 30 30\'%3e%3cpath stroke=\'rgba%28255, 255, 255, 0.75%29\' stroke-linecap=\'round\' stroke-miterlimit=\'10\' stroke-width=\'2\' d=\'M4 7h22M4 15h22M4 23h22\'/%3e%3c/svg%3e');"></span>
                    </button>
                    <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                        <ul class="navbar-nav flex-grow-1">
                            <li class="nav-item">
                                <a class="nav-link nav-link-custom" asp-area="" asp-page="/Index">
                                    <i class="fas fa-home me-1"></i>Home
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link nav-link-custom" asp-area="" asp-page="/Privacy">
                                    <i class="fas fa-shield-alt me-1"></i>Privacy
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </header>

        <main role="main" class="flex-grow-1">
            @RenderBody()
        </main>

        <footer class="footer-custom text-center">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <p class="mb-2">
                            <i class="fas fa-cloud-sun me-2"></i>
                            <strong>WeatherApp</strong> - Your Personal Weather Station
                        </p>
                        <p class="mb-0">
                            &copy; 2025 WeatherApp. Made with <i class="fas fa-heart text-danger"></i> for weather enthusiasts.
                            <a asp-area="" asp-page="/Privacy" class="ms-3">
                                <i class="fas fa-shield-alt me-1"></i>Privacy Policy
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </footer>
    </div>


    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>