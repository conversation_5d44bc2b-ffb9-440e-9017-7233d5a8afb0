using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using CBMSystem.Data;
using CBMSystem.Models;

namespace CBMSystem.Pages;

public class IndexModel : PageModel
{
    private readonly ILogger<IndexModel> _logger;
    private readonly CBMContext _context;

    public IndexModel(ILogger<IndexModel> logger, CBMContext context)
    {
        _logger = logger;
        _context = context;
    }

    public int TotalCustomers { get; set; }
    public int TotalProducts { get; set; }
    public int TotalOrders { get; set; }
    public int LowStockProducts { get; set; }
    public IList<Customer> RecentCustomers { get; set; } = new List<Customer>();
    public IList<Product> LowStockProductsList { get; set; } = new List<Product>();

    public async Task OnGetAsync()
    {
        // Get dashboard statistics
        TotalCustomers = await _context.Customers.CountAsync();
        TotalProducts = await _context.Products.CountAsync();
        TotalOrders = await _context.Orders.CountAsync();

        // Get low stock products count
        LowStockProducts = await _context.Products
            .Where(p => p.StockQuantity <= p.MinimumStockLevel)
            .CountAsync();

        // Get recent customers (last 5)
        RecentCustomers = await _context.Customers
            .Where(c => c.IsActive)
            .OrderByDescending(c => c.CreatedDate)
            .Take(5)
            .ToListAsync();

        // Get low stock products list (top 5)
        LowStockProductsList = await _context.Products
            .Where(p => p.StockQuantity <= p.MinimumStockLevel && p.IsActive)
            .OrderBy(p => p.StockQuantity)
            .Take(5)
            .ToListAsync();
    }
}
