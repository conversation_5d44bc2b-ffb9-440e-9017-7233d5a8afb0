using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using CBMSystem.Data;
using CBMSystem.Models;

namespace CBMSystem.Pages.Customers
{
    public class CreateModel : PageModel
    {
        private readonly CBMContext _context;

        public CreateModel(CBMContext context)
        {
            _context = context;
        }

        public IActionResult OnGet()
        {
            Customer = new Customer();
            return Page();
        }

        [BindProperty]
        public Customer Customer { get; set; } = default!;

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            Customer.CreatedDate = DateTime.UtcNow;
            _context.Customers.Add(Customer);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = $"Customer '{Customer.FullName}' has been created successfully.";
            return RedirectToPage("./Index");
        }
    }
}
