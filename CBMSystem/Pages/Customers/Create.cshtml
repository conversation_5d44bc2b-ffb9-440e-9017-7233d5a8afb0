@page
@model CBMSystem.Pages.Customers.CreateModel
@{
    ViewData["Title"] = "Create Customer";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-user-plus me-2"></i>Add New Customer
                </h2>
                <a asp-page="./Index" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Customers
                </a>
            </div>

            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-secondary mb-3">
                                    <i class="fas fa-user me-2"></i>Personal Information
                                </h5>
                                
                                <div class="mb-3">
                                    <label asp-for="Customer.FirstName" class="form-label">First Name *</label>
                                    <input asp-for="Customer.FirstName" class="form-control" />
                                    <span asp-validation-for="Customer.FirstName" class="text-danger"></span>
                                </div>
                                
                                <div class="mb-3">
                                    <label asp-for="Customer.LastName" class="form-label">Last Name *</label>
                                    <input asp-for="Customer.LastName" class="form-control" />
                                    <span asp-validation-for="Customer.LastName" class="text-danger"></span>
                                </div>
                                
                                <div class="mb-3">
                                    <label asp-for="Customer.Email" class="form-label">Email *</label>
                                    <input asp-for="Customer.Email" class="form-control" type="email" />
                                    <span asp-validation-for="Customer.Email" class="text-danger"></span>
                                </div>
                                
                                <div class="mb-3">
                                    <label asp-for="Customer.PhoneNumber" class="form-label">Phone Number</label>
                                    <input asp-for="Customer.PhoneNumber" class="form-control" type="tel" />
                                    <span asp-validation-for="Customer.PhoneNumber" class="text-danger"></span>
                                </div>
                                
                                <div class="mb-3">
                                    <label asp-for="Customer.Company" class="form-label">Company</label>
                                    <input asp-for="Customer.Company" class="form-control" />
                                    <span asp-validation-for="Customer.Company" class="text-danger"></span>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="text-secondary mb-3">
                                    <i class="fas fa-map-marker-alt me-2"></i>Address Information
                                </h5>
                                
                                <div class="mb-3">
                                    <label asp-for="Customer.Address" class="form-label">Address</label>
                                    <textarea asp-for="Customer.Address" class="form-control" rows="2"></textarea>
                                    <span asp-validation-for="Customer.Address" class="text-danger"></span>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="Customer.City" class="form-label">City</label>
                                            <input asp-for="Customer.City" class="form-control" />
                                            <span asp-validation-for="Customer.City" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="Customer.State" class="form-label">State/Province</label>
                                            <input asp-for="Customer.State" class="form-control" />
                                            <span asp-validation-for="Customer.State" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="Customer.PostalCode" class="form-label">Postal Code</label>
                                            <input asp-for="Customer.PostalCode" class="form-control" />
                                            <span asp-validation-for="Customer.PostalCode" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="Customer.Country" class="form-label">Country</label>
                                            <input asp-for="Customer.Country" class="form-control" />
                                            <span asp-validation-for="Customer.Country" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label asp-for="Customer.Notes" class="form-label">Notes</label>
                                    <textarea asp-for="Customer.Notes" class="form-control" rows="3" placeholder="Additional notes about this customer..."></textarea>
                                    <span asp-validation-for="Customer.Notes" class="text-danger"></span>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input asp-for="Customer.IsActive" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="Customer.IsActive" class="form-check-label">
                                        Active Customer
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a asp-page="./Index" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Customer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
