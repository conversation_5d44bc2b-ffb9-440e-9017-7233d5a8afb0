using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using CBMSystem.Data;
using CBMSystem.Models;

namespace CBMSystem.Pages.Customers
{
    public class IndexModel : PageModel
    {
        private readonly CBMContext _context;

        public IndexModel(CBMContext context)
        {
            _context = context;
        }

        public IList<Customer> Customers { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Customers = await _context.Customers
                .OrderBy(c => c.LastName)
                .ThenBy(c => c.FirstName)
                .ToListAsync();
        }
    }
}
