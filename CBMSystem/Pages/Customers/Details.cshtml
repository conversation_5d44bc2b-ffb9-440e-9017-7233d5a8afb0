@page
@model CBMSystem.Pages.Customers.DetailsModel
@{
    ViewData["Title"] = "Customer Details";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-user me-2"></i>Customer Details
                </h2>
                <div class="btn-group">
                    <a asp-page="./Edit" asp-route-id="@Model.Customer.CustomerId" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Customer
                    </a>
                    <a asp-page="./Index" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>Personal Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Full Name</label>
                                        <div class="fw-bold">@Model.Customer.FullName</div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Email</label>
                                        <div>
                                            <a href="mailto:@Model.Customer.Email" class="text-decoration-none">
                                                @Model.Customer.Email
                                            </a>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Phone Number</label>
                                        <div>
                                            @if (!string.IsNullOrEmpty(Model.Customer.PhoneNumber))
                                            {
                                                <a href="tel:@Model.Customer.PhoneNumber" class="text-decoration-none">
                                                    @Model.Customer.PhoneNumber
                                                </a>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Not provided</span>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Company</label>
                                        <div>@(Model.Customer.Company ?? "Not specified")</div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Status</label>
                                        <div>
                                            @if (Model.Customer.IsActive)
                                            {
                                                <span class="badge bg-success">Active</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">Inactive</span>
                                            }
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Customer Since</label>
                                        <div>@Model.Customer.CreatedDate.ToString("MMMM dd, yyyy")</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-map-marker-alt me-2"></i>Address Information
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.Customer.Address) || 
                                 !string.IsNullOrEmpty(Model.Customer.City) || 
                                 !string.IsNullOrEmpty(Model.Customer.State))
                            {
                                <div class="mb-3">
                                    <label class="form-label text-muted">Address</label>
                                    <div>@(Model.Customer.Address ?? "Not provided")</div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">City</label>
                                            <div>@(Model.Customer.City ?? "Not provided")</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">State/Province</label>
                                            <div>@(Model.Customer.State ?? "Not provided")</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Postal Code</label>
                                            <div>@(Model.Customer.PostalCode ?? "Not provided")</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label text-muted">Country</label>
                                    <div>@(Model.Customer.Country ?? "Not provided")</div>
                                </div>
                            }
                            else
                            {
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-map-marker-alt fa-2x mb-2"></i>
                                    <p>No address information provided</p>
                                </div>
                            }
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Customer.Notes))
                    {
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-sticky-note me-2"></i>Notes
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">@Model.Customer.Notes</p>
                            </div>
                        </div>
                    }
                </div>

                <div class="col-lg-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>Order Summary
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center">
                                <div class="display-6 fw-bold text-primary">@Model.Customer.Orders.Count</div>
                                <div class="text-muted">Total Orders</div>
                            </div>
                            @if (Model.Customer.Orders.Any())
                            {
                                <hr>
                                <div class="small">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Last Order:</span>
                                        <span>@Model.Customer.Orders.OrderByDescending(o => o.OrderDate).First().OrderDate.ToString("MMM dd, yyyy")</span>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <div class="card shadow-sm">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-cog me-2"></i>Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a asp-page="/Orders/Create" asp-route-customerId="@Model.Customer.CustomerId" 
                                   class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Create Order
                                </a>
                                <a asp-page="./Edit" asp-route-id="@Model.Customer.CustomerId" 
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-edit me-2"></i>Edit Customer
                                </a>
                                <a asp-page="./Delete" asp-route-id="@Model.Customer.CustomerId" 
                                   class="btn btn-outline-danger">
                                    <i class="fas fa-trash me-2"></i>Delete Customer
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
