@page
@model CBMSystem.Pages.Customers.IndexModel
@{
    ViewData["Title"] = "Customers";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-users me-2"></i>Customer Management
                </h2>
                <a asp-page="./Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add New Customer
                </a>
            </div>

            @if (Model.Customers.Any())
            {
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Company</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var customer in Model.Customers)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle me-2">
                                                        @customer.FirstName.Substring(0, 1)@customer.LastName.Substring(0, 1)
                                                    </div>
                                                    <div>
                                                        <strong>@customer.FullName</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="mailto:@customer.Email" class="text-decoration-none">
                                                    @customer.Email
                                                </a>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(customer.PhoneNumber))
                                                {
                                                    <a href="tel:@customer.PhoneNumber" class="text-decoration-none">
                                                        @customer.PhoneNumber
                                                    </a>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>@(customer.Company ?? "-")</td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(customer.City) && !string.IsNullOrEmpty(customer.State))
                                                {
                                                    <span>@customer.City, @customer.State</span>
                                                }
                                                else if (!string.IsNullOrEmpty(customer.City))
                                                {
                                                    <span>@customer.City</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (customer.IsActive)
                                                {
                                                    <span class="badge bg-success">Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">Inactive</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="./Details" asp-route-id="@customer.CustomerId" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-page="./Edit" asp-route-id="@customer.CustomerId" 
                                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-page="./Delete" asp-route-id="@customer.CustomerId" 
                                                       class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No customers found</h4>
                    <p class="text-muted">Get started by adding your first customer.</p>
                    <a asp-page="./Create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add First Customer
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 14px;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
    }
    
    .btn-group .btn {
        margin-right: 2px;
    }
    
    .btn-group .btn:last-child {
        margin-right: 0;
    }
</style>
