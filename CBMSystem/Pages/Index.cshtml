﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </h2>
                <div class="text-muted">
                    <i class="fas fa-calendar me-1"></i>@DateTime.Now.ToString("MMMM dd, yyyy")
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Customers
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalCustomers</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Products
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalProducts</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Orders
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalOrders</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Low Stock Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.LowStockProducts</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a asp-page="/Customers/Create" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-user-plus fa-2x mb-2"></i>
                                <br>Add Customer
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-page="/Products/Create" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-plus-square fa-2x mb-2"></i>
                                <br>Add Product
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-page="/Orders/Create" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                <br>New Order
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-page="/Reports/Index" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <br>View Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>Recent Customers
                    </h6>
                </div>
                <div class="card-body">
                    @if (Model.RecentCustomers.Any())
                    {
                        @foreach (var customer in Model.RecentCustomers)
                        {
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar-circle me-3">
                                    @customer.FirstName.Substring(0, 1)@customer.LastName.Substring(0, 1)
                                </div>
                                <div class="flex-grow-1">
                                    <div class="font-weight-bold">@customer.FullName</div>
                                    <div class="text-muted small">@customer.Email</div>
                                </div>
                                <div class="text-muted small">
                                    @customer.CreatedDate.ToString("MMM dd")
                                </div>
                            </div>
                        }
                        <div class="text-center">
                            <a asp-page="/Customers/Index" class="btn btn-sm btn-outline-primary">
                                View All Customers
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <p>No customers yet</p>
                            <a asp-page="/Customers/Create" class="btn btn-primary btn-sm">Add First Customer</a>
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-box me-2"></i>Low Stock Alert
                    </h6>
                </div>
                <div class="card-body">
                    @if (Model.LowStockProductsList.Any())
                    {
                        @foreach (var product in Model.LowStockProductsList)
                        {
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <div>
                                    <div class="font-weight-bold">@product.Name</div>
                                    <div class="text-muted small">SKU: @product.SKU</div>
                                </div>
                                <div class="text-right">
                                    <span class="badge bg-warning">@product.StockQuantity left</span>
                                </div>
                            </div>
                        }
                        <div class="text-center">
                            <a asp-page="/Products/Index" class="btn btn-sm btn-outline-primary">
                                View All Products
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-2x mb-2 text-success"></i>
                            <p>All products are well stocked!</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }

    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }

    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }

    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }

    .text-gray-300 {
        color: #dddfeb !important;
    }

    .text-gray-800 {
        color: #5a5c69 !important;
    }

    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 14px;
    }
</style>
