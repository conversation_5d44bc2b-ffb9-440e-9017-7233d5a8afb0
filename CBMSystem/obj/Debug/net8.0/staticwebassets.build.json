{"Version": 1, "Hash": "8APVm2Muc+HW/CMYql4MgOjc5G5/BrOGz/g1trpwaSY=", "Source": "CBMSystem", "BasePath": "_content/CBMSystem", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "CBMSystem/wwwroot", "Source": "CBMSystem", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "Pattern": "**"}], "Assets": [{"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/obj/Debug/net8.0/scopedcss/bundle/CBMSystem.styles.css", "SourceId": "CBMSystem", "SourceType": "Computed", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/obj/Debug/net8.0/scopedcss/bundle/", "BasePath": "_content/CBMSystem", "RelativePath": "CBMSystem.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/obj/Debug/net8.0/scopedcss/bundle/CBMSystem.styles.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/obj/Debug/net8.0/scopedcss/projectbundle/CBMSystem.bundle.scp.css", "SourceId": "CBMSystem", "SourceType": "Computed", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/obj/Debug/net8.0/scopedcss/projectbundle/", "BasePath": "_content/CBMSystem", "RelativePath": "CBMSystem.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/obj/Debug/net8.0/scopedcss/projectbundle/CBMSystem.bundle.scp.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/css/site.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/site.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/favicon.ico", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/favicon.ico"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/js/site.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/site.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/bootstrap/LICENSE", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/LICENSE"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery-validation/dist/additional-methods.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.min.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.min.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery-validation/LICENSE.md", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/LICENSE.md"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery/dist/jquery.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery/dist/jquery.min.js", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.js"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery/dist/jquery.min.map", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.map"}, {"Identity": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/lib/jquery/LICENSE.txt", "SourceId": "CBMSystem", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/Dev/cSharp/CBMSystem/wwwroot/", "BasePath": "_content/CBMSystem", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/LICENSE.txt"}]}