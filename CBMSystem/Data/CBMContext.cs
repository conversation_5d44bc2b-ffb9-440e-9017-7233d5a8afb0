using Microsoft.EntityFrameworkCore;
using CBMSystem.Models;

namespace CBMSystem.Data
{
    public class CBMContext : DbContext
    {
        public CBMContext(DbContextOptions<CBMContext> options) : base(options)
        {
        }

        // DbSets for all entities
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Customer entity
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.CustomerId);
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
            });

            // Configure Product entity
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasKey(e => e.ProductId);
                entity.HasIndex(e => e.SKU).IsUnique();
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.Price).HasPrecision(18, 2);
                entity.Property(e => e.CostPrice).HasPrecision(18, 2);
            });

            // Configure Order entity
            modelBuilder.Entity<Order>(entity =>
            {
                entity.HasKey(e => e.OrderId);
                entity.HasIndex(e => e.OrderNumber).IsUnique();
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.SubTotal).HasPrecision(18, 2);
                entity.Property(e => e.TaxAmount).HasPrecision(18, 2);
                entity.Property(e => e.ShippingAmount).HasPrecision(18, 2);
                entity.Property(e => e.DiscountAmount).HasPrecision(18, 2);
                entity.Property(e => e.TotalAmount).HasPrecision(18, 2);

                // Configure relationship with Customer
                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.Orders)
                      .HasForeignKey(e => e.CustomerId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure OrderItem entity
            modelBuilder.Entity<OrderItem>(entity =>
            {
                entity.HasKey(e => e.OrderItemId);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.UnitPrice).HasPrecision(18, 2);
                entity.Property(e => e.DiscountAmount).HasPrecision(18, 2);
                entity.Property(e => e.LineTotal).HasPrecision(18, 2);

                // Configure relationship with Order
                entity.HasOne(e => e.Order)
                      .WithMany(o => o.OrderItems)
                      .HasForeignKey(e => e.OrderId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure relationship with Product
                entity.HasOne(e => e.Product)
                      .WithMany(p => p.OrderItems)
                      .HasForeignKey(e => e.ProductId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Seed some initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed Categories and sample products
            modelBuilder.Entity<Product>().HasData(
                new Product
                {
                    ProductId = 1,
                    Name = "Laptop Computer",
                    Description = "High-performance laptop for business use",
                    SKU = "LAP-001",
                    Price = 1299.99m,
                    CostPrice = 899.99m,
                    StockQuantity = 25,
                    MinimumStockLevel = 5,
                    Category = "Electronics",
                    Brand = "TechBrand",
                    Unit = "piece",
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                },
                new Product
                {
                    ProductId = 2,
                    Name = "Wireless Mouse",
                    Description = "Ergonomic wireless mouse with long battery life",
                    SKU = "MOU-001",
                    Price = 29.99m,
                    CostPrice = 15.99m,
                    StockQuantity = 100,
                    MinimumStockLevel = 20,
                    Category = "Electronics",
                    Brand = "TechBrand",
                    Unit = "piece",
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                },
                new Product
                {
                    ProductId = 3,
                    Name = "Office Chair",
                    Description = "Comfortable ergonomic office chair",
                    SKU = "CHR-001",
                    Price = 199.99m,
                    CostPrice = 120.00m,
                    StockQuantity = 15,
                    MinimumStockLevel = 3,
                    Category = "Furniture",
                    Brand = "ComfortSeating",
                    Unit = "piece",
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                }
            );

            // Seed sample customers
            modelBuilder.Entity<Customer>().HasData(
                new Customer
                {
                    CustomerId = 1,
                    FirstName = "John",
                    LastName = "Smith",
                    Email = "<EMAIL>",
                    PhoneNumber = "******-0123",
                    Company = "ABC Corporation",
                    Address = "123 Business St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA",
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                },
                new Customer
                {
                    CustomerId = 2,
                    FirstName = "Sarah",
                    LastName = "Johnson",
                    Email = "<EMAIL>",
                    PhoneNumber = "******-0456",
                    Company = "XYZ Solutions",
                    Address = "456 Commerce Ave",
                    City = "Los Angeles",
                    State = "CA",
                    PostalCode = "90210",
                    Country = "USA",
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                }
            );
        }
    }
}
